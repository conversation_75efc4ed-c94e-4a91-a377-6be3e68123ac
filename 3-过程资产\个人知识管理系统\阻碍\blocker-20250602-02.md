---
aliases:
  - KR进度描述方法缺失
created_Date: 2025-06-02
status: 进行中
count: 2
timeCost: 2
drain_val: 4
relation: 
cssclasses:
  - c3
---
# 1. 基础信息

- 现象描述：如何进行KR进度描述才更有意义？
- 直接影响：制定下周目标时全凭感觉，无法判断其合理性和紧迫性
- 衍生影响：

# 2. 临时方案

| 生效时间             | 目标          | 临时方案描述                                                                                                                                                                                                                                                                                                                         | 决策依据                                           | 已知风险与局限                                       | 状态跟踪 | 债务等级 | 知识缺口 |
| ---------------- | ----------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ---------------------------------------------- | --------------------------------------------- | ---- | ---- | ---- |
| 2025-06-02 14:00 | 定义KR描述的初始框架 | （1）列举具体文档/反馈/行为记录<br>（2）说明于目标逻辑链的关系-->本周证据->影响KR的哪个环节（增强假设/验证路径/消除风险）->如何降低目标的不确定性<br>（3）风险降级说明风险，风险升级需预警                                                                                                                                                                                                                      | 1、结构化处理有利于降低认知负担                               | KR并非100%需要量化，复盘阶段针对量化的KR看数据变化趋势，非量化的KR看证据强度积累 | 已失效  |      |      |
| 2025-06-29 16:00 | 补充细化初始框架    | 1、定位KR类型（标准型KR/探索型KR）<br>2、根据识别的KR类型选择针对性方案<br>3、针对探索性KR（难以量化）<br>（1）我们需要通过本周/本迭代的工作回答什么关键问题？（识别关键验证点）<br>（2）需要获取哪些信息才能推进决策？（识别关键决策点）<br>（3）通过「具体活动」，我们确认了/发现了/排除了 「具体发现、验证的假设、消除的选项、识别的关键风险/机会」（关键认知/验证）<br>（4）基于以上认知，我们决定 「具体下一步行动/决策/优先级调整」，这将推动KR向 「下一步目标]」迈进（决策支撑/方向明确）<br>4、针对标准型KR（「行动路径」+「量化结果」）<br>（1）这些交付物对KR指标的具体影响 | 1、通过定位KR类型可以快速选择针对性的方案<br>2、明确了探索性KR的引导问题，便于思考 |                                               |      |      |      |
# 3. 根因分析

- 核心问题是什么？ --> 

# 5. 最终方案（可选）

| 生效时间 | 根本原因 | 方案描述 | 决策依据 | 已知风险与局限 |
| ---- | ---- | ---- | ---- | ------- |
|      |      |      |      |         |
