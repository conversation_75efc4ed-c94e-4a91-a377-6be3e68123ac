---
homePageLink: "[[PKMS-首页]]"
---
# 1. [[字段提示信息表#周目标描述： f37322|周目标]] 

- 为杜绝风险问题积压、提升系统改善效率，于本周五18:00前建立「改善」闭环工作流机制，需交付以下经确认的交付物

# 2. [[字段提示信息表#验收标准描述 19887e|验收标准]] 
| 序号  | 交付物             | 标准/通过条件                                                                                                                                                                                             | 备注  |
| --- | --------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --- |
| 1   | 「改进待办清单」V1.0    | 必须包含的功能：<br>（1）洞见/闪念模块用以记录项目执行过程中所有的改进想法<br>（2）可执行任务木块用以记录项目执行期间所有的改善行动                                                                                                                             |     |
| 4   | 「看板工具」          | 使用的「看板工具」不能有一下内容<br>（1）包含功能重复的查询代码                                                                                                                                                                  |     |
| 5   | 「每日执行」模板V3.0    | 优化部分必须包含的功能：<br>（2）包含所有最新的改进内容<br>优化部分不能包含的功能：<br>（1）存在“闪念“或”洞见/发现“模块                                                                                                                               |     |
| 6   | 「每周计划」模板V3.0    | 优化部分必须包含的功能：<br>（1）包含「改进事项」的所有关键信息（任务&想法）<br>（2）「改进事项」信息清单必须经过排序<br>（3）包含「技术债」的所有相关信息<br>（4）「技术债」信息清单必须经过排序，条目包含技术债的类型和优先级信息<br>（5）以callout形式展示查询信息<br>（6）包含所有最新的改进内容<br>优化部分不能包含的功能：<br>（1）以链接形式嵌入 |     |
| 7   | 「每周评审」模板V2.0    | 优化部分必须包含的功能：<br>（1）「改进事项」成果相关信息<br>（2）包含所有最新的改进内容<br>优化部分不能包含的功能：<br>（1）存在”阻碍“成果相关信息                                                                                                                |     |
| 8   | 「项目首页」模板V2.0    | 优化部分必须包含的功能：<br>（1）「每周评审」”交付异常“累计汇总信息<br>（2）包含所有最新的改进内容                                                                                                                                             |     |
| 9   | 「自动创建洞见/任务」脚本文件 | 核心功能必须包含：<br>（1）弹出选择弹窗让用户选择"洞见"或"可执行任务"<br>（2）弹出输入弹窗让用户输入任务内容<br>（3）在指定文件的对应标题下添加新任务                                                                                                                |     |
# 3. 改进事项
> [!dashboard]
> 
> > [!tip] 洞见/发现
> >```tasks
> >not done
> >limit 5
> >heading includes 洞见
> >description regex matches /\S/
> >hide backlink
> >sort by priority
> >filter by function \
> >const projectName = query.file.path.split("/")[1]; \
> >const targetPath = `2-项目/${projectName}/改进待办事项`; \
> >const pathMatches = task.file.path.includes(targetPath);\
> >return pathMatches;
> >sort by function \
> >const tagOrder = ["风险预警", "流程优化", "创新方案", "新增需求", "问题探索"];\
> >const matchedTag = tagOrder.find(tag => task.description.includes(tag));\
> >const order = matchedTag ? tagOrder.indexOf(matchedTag) : tagOrder.length;\
> >return order;
> >```
> 
> > [!todo] 碎片改进
> >```tasks
> >not done
> >limit 5
> >heading includes 任务
> >description regex matches /\S/
> >hide backlink
> >sort by priority
> >filter by function \
> >const projectName = query.file.path.split("/")[1]; \
> >const targetPath = `2-项目/${projectName}/改进待办事项`; \
> >const pathMatches = task.file.path.includes(targetPath);\
> >return pathMatches;
> >```
# 4. 技术债
```dataviewjs
	// 统一配置：类型和优先级设置
	const TECH_DEBT_CONFIG = {
	    types: {
	        presets: ["阻塞型", "成本型", "战略型", "无害型"],
	        get set() { return new Set(this.presets); }
	    },
	    priorities: {
	        presets: ["立即", "高", "中", "低"],
	        get set() { return new Set(this.presets); },
	        get order() { 
	            const order = {};
	            this.presets.forEach((p, i) => order[p] = i + 1);
	            return order;
	        }
	    }
	};
	
	// 获取技术债文件
	const projectName = dv.current().file.path.split("/")[1];
	const folderPath = `3-过程资产/${projectName}/技术债`;
	const allTechDebtFiles = dv.pages(`"${folderPath}"`)
	    .filter(p => 
	        p.file.name.match(/^td-\d{8}-\d{2}$/) && 
	        p.status !== "已解决" &&
	        p.file.name !== "dashboard"
	    );
	
	// 检测非预设值文件
	const hasInvalidValue = (file) => {
	    const type = file.type?.toString();
	    const priority = file.priority?.toString();
	    
	    return !TECH_DEBT_CONFIG.types.set.has(type) || 
	           !TECH_DEBT_CONFIG.priorities.set.has(priority);
	};
	
	const invalidFiles = allTechDebtFiles.filter(hasInvalidValue);
	
	// 辅助函数：获取类型显示值（仅显示预设值或提示信息）
	const getTypeDisplay = (file) => {
	    const type = file.type?.toString();
	    if (!type) return "(未设置类型)";
	    return TECH_DEBT_CONFIG.types.set.has(type) ? type : "(无效类型)";
	};
	
	// 辅助函数：获取优先级显示值（仅显示预设值或提示信息）
	const getPriorityDisplay = (file) => {
	    const priority = file.priority?.toString();
	    if (!priority) return "(未设置优先级)";
	    return TECH_DEBT_CONFIG.priorities.set.has(priority) ? priority : "(无效优先级)";
	};
	
	// 显示结果
	if (invalidFiles.length > 0) {
	    // 输出非预设值文件  
	    const listItems = invalidFiles.map(file => {
	        const displayName = file.aliases || file.file.name;
	        const typeDisplay = getTypeDisplay(file);
	        const priorityDisplay = getPriorityDisplay(file);
	        return `[[${file.file.name}|${displayName}]] - ${typeDisplay} - ${priorityDisplay}`;
	    });
	    
	    // 使用单个Markdown列表输出
	    dv.paragraph(listItems);
	} else {
	    // 如果没有非预设值文件，则按原逻辑处理预设值文件
	    const validFiles = allTechDebtFiles;
	    // 确定目标类型
	    let targetType = null;
	    for (const type of TECH_DEBT_CONFIG.types.presets) {
	        if (validFiles.some(p => p.type === type)) {
	            targetType = type;
	            break;
	        }
	    }
	    if (!targetType) {
	        dv.paragraph("⚠️ 没有待处理的技术债");
	    } else {
	        // 过滤并排序文件
	        const filteredFiles = validFiles
	            .filter(p => p.type === targetType)
	            .sort(p => TECH_DEBT_CONFIG.priorities.order[p.Priority] || 999);
	        
	        if (filteredFiles.length === 0) {
	            dv.paragraph(`⚠️ 没有${targetType}类型的技术债`);
	        } else {          
	            // 创建优先级分组
	            const groupedByPriority = {};
	            filteredFiles.forEach(file => {
	                const priority = file.priority || "未设置";
	                if (!groupedByPriority[priority]) {
	                    groupedByPriority[priority] = [];
	                }
	                groupedByPriority[priority].push(file);
	            });
	            
	            // 收集所有输出行
	            const outputLines = [];
	            TECH_DEBT_CONFIG.priorities.presets.forEach(priority => {
	                if (groupedByPriority[priority]) {
	                    groupedByPriority[priority].forEach(file => {
	                        const displayName = file.aliases || file.file.name;
	                        outputLines.push(
	                            `[[${file.file.name}|${displayName}]] - ${file.type} - ${priority}`
	                        );
	                    });
	                }
	            });
	            // 使用单个Markdown列表输出所有内容
	            dv.paragraph(outputLines);
	        }
	    }
	}
```
# 5. 任务拆解

- [x] 常见独立于组件的「改进待办事项」清单，并设置“洞见/发现”、“可执行任务”模块 ⏳ 2025-07-10 ✅ 2025-07-10
- [x] 将「每周计划」“障碍回顾”替换为“改进事项”，重新编写dataviewjs查询代码 ⏳ 2025-07-11 ✅ 2025-07-11
- [x] 删除「每周计划」“技术债”嵌入链接，替换为dataviewjs查询代码 ⏳ 2025-07-11 ✅ 2025-07-11
- [x] 优化「每周计划」”改进事项“模块代码，使其仅展示优先级最高的前5条信息 ⏳ 2025-07-11 ✅ 2025-07-11
- [x] 优化「每周计划」”改进事项“模块代码，增加”优先级“由高到低排序功能 ⏳ 2025-07-11 ✅ 2025-07-11
- [x] 按照最新框架修正「每周计划」模板 ⏳ 2025-07-11 ✅ 2025-07-11
- [x] 删除「看板工具」“技术债统计”及其相关代码 ⏳ 2025-07-11 ✅ 2025-07-11
- [x] 删除「看板工具」“闪念统计”及其相关代码 ⏳ 2025-07-11 ✅ 2025-07-11
- [x] 删除「看板工具」“上周阻碍统计”及其相关代码 ⏳ 2025-07-11 ✅ 2025-07-11
- [x] 将「每日执行」组件中的“洞见/发现”转移至「改进事项」清单 ⏳ 2025-07-11 ✅ 2025-07-11
- [x] 删除历史文件中「每日执行」组件“洞见/发现”或相关模块的全部内容 ⏳ 2025-07-11 ✅ 2025-07-11
- [x] 按照最新框架修正「每日执行」模板 ⏳ 2025-07-11 ✅ 2025-07-11
- [x] 编写自动创建”洞见/发现“或者”可执行任务“脚本 ⏳ 2025-07-11 ✅ 2025-07-11
- [x] 将「每周评审」看板中“阻碍”替换为“改善”，并重新编写dataviewjs查询代码 ⏳ 2025-07-11 ✅ 2025-07-11
- [x] 删除「每周评审」“闪念”模块所有相关内容 ⏳ 2025-07-11 ✅ 2025-07-11
- [x] 为「项目首页」添加“交付异常”，并编写dataviewjs查询代码（累计周评审“交付异常”，按照时间倒序） ⏳ 2025-07-11 ✅ 2025-07-11
- [x] 〖改善〗将「每周计划」中的三栏看板调整为两栏，并将“阻碍”移除单独形成一个模块 ⏳ 2025-07-11 ✅ 2025-07-11
- [x] 〖改善〗修复「每日执行」“阻碍”代码结果显示异常问题 ⏳ 2025-07-11 ✅ 2025-07-11
- [x] 〖改善〗修复「每周回顾」“改善回顾”统计“流程改善”空白数据行的问题 ⏳ 2025-07-11 ✅ 2025-07-11