---
aliases:
  - 阻碍记录方法缺失
created_Date: 2025-07-07
status: 进行中
count: 1
timeCost: 1
drain_val: 3
relation:
  - "[[blocker-20250606-01]]"
cssclasses:
  - c3
---
# 1. 基础信息

- 现象描述：什么样的问题应该视为阻碍？什么样的阻碍应该被记录？
- 直接影响：新阻碍被遗漏，旧阻碍被覆盖
- 衍生影响：问题演进路径丢失，不利于后问题解决和复盘

# 2. 临时方案

| 生效时间                | 目标               | 临时方案描述                                                                                                     | 决策依据                   | 已知风险与局限 | 状态跟踪 | 债务等级 | 知识缺口 |
| ------------------- | ---------------- | ---------------------------------------------------------------------------------------------------------- | ---------------------- | ------- | ---- | ---- | ---- |
| 2025-07-08 10:03:35 | 保证影响当前工作的问题被全部记录 | 1、IF“遇到问题”&“影响当前工作推进”，“阻碍”；<br>2、否则，“闪念“；                                                                  | “影响当前工作推进”的问题能够体现阻碍的本质 | 判断条件模糊  | 已失效  |      |      |
| 2025-07-10 17:58:20 | 细化阻碍判断的条件        | 1、IF“遇到问题”&“降低了向目标前进的速度或效率”，“阻碍”；<br>2、IF “产生疑问”&“为实现短期目标而牺牲长期质量或可维护性的妥协决策或疏漏”，“技术债”；<br>3、否则，“闪念”或“洞见/发现” |                        |         |      |      |      |
# 3. 根因分析

- 核心问题是什么？ --> 

# 5. 最终方案（可选）

| 生效时间 | 根本原因 | 方案描述 | 决策依据 | 已知风险与局限 |
| ---- | ---- | ---- | ---- | ------- |
|      |      |      |      |         |
