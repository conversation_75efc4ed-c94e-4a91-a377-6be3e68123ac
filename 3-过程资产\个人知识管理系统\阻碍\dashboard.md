---
search:
---

```dataviewjs
const projectName = dv.current().file.path.split("/")[1];
const folderPath = `3-过程资产/${projectName}/阻碍`;
const files = dv.pages(`"${folderPath}"`)
    .filter(p => p.file.name !== dv.current().file.name)
    .sort(p => p.drain_val || 0, "desc");  // 按drain_val降序排序

dv.table(
    ["序号", "文件链接", "别名", "精力消耗", "次数", "创建日期", "耗时", "状态"],
    files.map((p, index) => {
        // 处理别名字段（数组转字符串）
        const aliases = p.aliases 
            ? (Array.isArray(p.aliases) ? p.aliases.join(", ") : p.aliases)
            : "-";
        
        // 更健壮的日期格式化处理
        let formattedDate = "-";
        if (p.created_Date) {
            const dateObj = dv.date(p.created_Date);
            
            if (dateObj && dateObj.isValid) {
                // 直接从 Luxon DateTime 对象获取日期组件
                const year = dateObj.year;
                const month = dateObj.month; // 1-12
                const day = dateObj.day.toString().padStart(2, '0');
                
                formattedDate = `${year}/${month}/${day}`;
            } else {
                // 如果无法解析为日期，直接使用原始值
                formattedDate = p.created_Date.toString();
            }
        }
            
        return [
            index + 1,                      // 自动编号
            p.file.link,                    // 文件链接
            aliases,                        // 别名
            p.drain_val || "缺失",             // 精力消耗
            p.count || "缺失",                 // 次数
            formattedDate,                  // 格式化的创建日期
            p.timeCost || "缺失",              // 耗时
            p.status || "缺失"                 // 状态（最后一列）
        ];
    })
);
```