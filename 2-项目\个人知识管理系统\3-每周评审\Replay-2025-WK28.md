---
homePageLink: "[[PKMS-首页]]"
本周计划: "[[Plan-2025-WK26]]"
---
# 1.  [[字段提示信息表#成果验收 59b696|验收成果]] 
> [!dashboard]
> 
> > [!todo] 目标
> > ```dataviewjs
> >// 配置：设置主要关注的标签类型
> >const primaryTag = "#目标"; // 当日程条目都已标记时，筛选此标签
> >// 获取当前笔记文件名并解析周数信息
> >const fileName = dv.current().file.name;
> >const weekMatch = fileName.match(/Replay-(\d{4})-WK(\d+)/);
> >const planFileName = `${fileName.replace("Replay","Plan")}.md`;
> >if (!weekMatch) {
> >    dv.paragraph("**错误**：文件名格式不符合要求：Replay-YYYY-WKWW");
> >} else {
> >    const year = parseInt(weekMatch[1]);
> >    const currentWeek = parseInt(weekMatch[2]);
> >    const projectName = dv.current().file.path.split("/")[1];
> >    const path = `2-项目/${projectName}/2-每日执行`;
> >    // 计算ISO周数的函数
> >    function getISOWeek(date) {
> >        const d = new Date(date);
> >        d.setHours(0,0,0,0);
> >        d.setDate(d.getDate() + 4 - (d.getDay() || 7));
> >        const yearStart = new Date(d.getFullYear(),0,1);
> >        const weekNo = Math.ceil(((d - yearStart) / 86400000 + 1)/7);
> >        return weekNo;
> >    }
> >    // 获取该路径下的所有文件
> >    const allFiles = dv.pages(`"${path}"`).file;
> >    // 存储结果
> >    const untaggedFiles = new Map(); // 未打标签的文件
> >    const primaryTagItems = [];      // 主要标签的条目原始内容
> >    let hasUntaggedEntries = false;  // 是否有未标记条目
> >    let totalEntries = 0;            // 总条目数
> >    // 筛选属于指定年份和周数的文件
> >    for (let file of allFiles) {
> >        const dateMatch = file.name.match(/Do-(\d{4})-(\d{2})-(\d{2})/);
> >        if (!dateMatch) continue;
> >        try {
> >            const dateStr = `${dateMatch[1]}-${dateMatch[2]}-${dateMatch[3]}`;
> >            const fileDate = new Date(dateStr);
> >            if (isNaN(fileDate.getTime())) continue;
> >            // 获取文件的年份和周数
> >            const fileYear = fileDate.getFullYear();
> >            const fileWeek = getISOWeek(fileDate);
> >            // 检查是否匹配当前年份和周数
> >            if (fileYear === year && fileWeek === currentWeek) {
> >                let untaggedCount = 0;
> >                // 检查文件中的列表项
> >                if (file.lists) {
> >                    for (let list of file.lists) {
> >                        if (list.header?.subpath?.includes("输出") && 
> >                            list.task === false &&
> >                            list.text) {
> >                            totalEntries++;
> >                            // 检查条目标签状态
> >                            const hasTag = list.text.includes("#阻碍") || list.text.includes("#技术债") || 
> >                                         list.text.includes("#目标");
> >                            if (!hasTag) {
> >                                untaggedCount++;
> >                                hasUntaggedEntries = true;
> >                            } else if (list.text.includes(primaryTag)) {
> >                                const cleanText = list.text.replace(primaryTag, "").trim();
> >                                primaryTagItems.push(cleanText);
> >                            }
> >                        }
> >                    }
> >                }
> >                // 记录未打标签的文件
> >                if (untaggedCount > 0) {
> >                    untaggedFiles.set(file.path, {
> >                        fileName: file.name,
> >                        count: untaggedCount
> >                    });
> >                }
> >            }
> >        } catch (e) {
> >            console.error("Error processing file:", file.name, e);
> >        }
> >    }
> >    // 智能输出结果
> >    if (untaggedFiles.size > 0) {
> >        // 输出未打标签的文件信息
> >        const fileLinks = [];
> >        for (let [filePath, fileData] of untaggedFiles.entries()) {
> >            const displayName = fileData.fileName.replace(".md", "").replace("Do-", "");
> >            fileLinks.push(`[[${filePath}|${displayName}]] (${fileData.count} 条)`);
> >        }
> >        fileLinks.sort();
> >        dv.paragraph(`以下 ${untaggedFiles.size} 个文件包含未标记条目：`);
> >        dv.paragraph(fileLinks);
> >    } else if (primaryTagItems.length > 0) {
> >        
> >        // 输出主要标签的条目原始信息
> >        dv.el("p",`${primaryTag.replace("#","")}成果：已完成 ${primaryTagItems.length} 个；`);
> >        dv.paragraph(primaryTagItems);
> >       
> >    } else {
> >        if (totalEntries > 0) {
> >            dv.paragraph(`本周无**${primaryTag.replace("#","")}型**成果输出`);
> >        } else {
> >            dv.paragraph("本周没有找到任何条目");
> >        }
> >    }
> >    // 未完成任务统计
> >	const planPath = `2-项目/${projectName}/1-每周规划`;
> >	const planFile = dv.page(`${planPath}/${planFileName}`);
> >	const taskSection = planFile.file.tasks
> >	?.filter(t => t.header?.subpath?.toLowerCase().includes("任务"))
> >	.filter(t =>t.text?.includes(`⟦${primaryTag.replace("#","")}⟧`));
> >	const targetTask = taskSection.filter(t => !t.checked);
> >	// 输出任务
> >	dv.el("p",`${primaryTag.replace("#","")}任务：本周新增任务 ${taskSection.length} 条，` 
> >	+ `其中 ${targetTask.length} 条未完成；`);
> >	dv.taskList(targetTask,false);
> >}
> >```
> 
> > [!tip] 改善
> > ```dataviewjs
> >// 配置：设置主要关注的标签类型
> >const primaryTag = "#改善"; // 当日程条目都已标记时，筛选此标签
> >// 获取当前笔记文件名并解析周数信息
> >const fileName = dv.current().file.name;
> >const weekMatch = fileName.match(/Replay-(\d{4})-WK(\d+)/);
> >const planFileName = `${fileName.replace("Replay","Plan")}.md`;
> >if (!weekMatch) {
> >    dv.paragraph("**错误**：文件名格式不符合要求：Replay-YYYY-WKWW");
> >} else {
> >    const year = parseInt(weekMatch[1]);
> >    const currentWeek = parseInt(weekMatch[2]);
> >    const projectName = dv.current().file.path.split("/")[1];
> >    const path = `2-项目/${projectName}/2-每日执行`;
> >    // 计算ISO周数的函数
> >    function getISOWeek(date) {
> >        const d = new Date(date);
> >        d.setHours(0,0,0,0);
> >        d.setDate(d.getDate() + 4 - (d.getDay() || 7));
> >        const yearStart = new Date(d.getFullYear(),0,1);
> >        const weekNo = Math.ceil(((d - yearStart) / 86400000 + 1)/7);
> >        return weekNo;
> >    }
> >    // 获取该路径下的所有文件
> >    const allFiles = dv.pages(`"${path}"`).file;
> >    // 存储结果
> >    const untaggedFiles = new Map(); // 未打标签的文件
> >    const primaryTagItems = [];      // 主要标签的条目原始内容
> >    let hasUntaggedEntries = false;  // 是否有未标记条目
> >    let totalEntries = 0;            // 总条目数
> >    // 筛选属于指定年份和周数的文件
> >    for (let file of allFiles) {
> >        const dateMatch = file.name.match(/Do-(\d{4})-(\d{2})-(\d{2})/);
> >        if (!dateMatch) continue;
> >        try {
> >            const dateStr = `${dateMatch[1]}-${dateMatch[2]}-${dateMatch[3]}`;
> >            const fileDate = new Date(dateStr);
> >            if (isNaN(fileDate.getTime())) continue;
> >            // 获取文件的年份和周数
> >            const fileYear = fileDate.getFullYear();
> >            const fileWeek = getISOWeek(fileDate);
> >            // 检查是否匹配当前年份和周数
> >            if (fileYear === year && fileWeek === currentWeek) {
> >                let untaggedCount = 0;
> >                // 检查文件中的列表项
> >                if (file.lists) {
> >                    for (let list of file.lists) {
> >                        if (list.header?.subpath?.includes("输出") && 
> >                            list.task === false &&
> >                            list.text) {
> >                            totalEntries++;
> >                            // 检查条目标签状态
> >                            const hasTag = list.text.includes("#阻碍") || list.text.includes("#技术债") || 
> >                                         list.text.includes("#目标");
> >                            if (!hasTag) {
> >                                untaggedCount++;
> >                                hasUntaggedEntries = true;
> >                            } else if (list.text.includes(primaryTag)) {
> >                                const cleanText = list.text.replace(primaryTag, "").trim();
> >                                primaryTagItems.push(cleanText);
> >                            }
> >                        }
> >                    }
> >                }
> >                // 记录未打标签的文件
> >                if (untaggedCount > 0) {
> >                    untaggedFiles.set(file.path, {
> >                        fileName: file.name,
> >                        count: untaggedCount
> >                    });
> >                }
> >            }
> >        } catch (e) {
> >            console.error("Error processing file:", file.name, e);
> >        }
> >    }
> >    // 智能输出结果
> >    if (untaggedFiles.size > 0) {
> >        // 输出未打标签的文件信息
> >        const fileLinks = [];
> >        for (let [filePath, fileData] of untaggedFiles.entries()) {
> >            const displayName = fileData.fileName.replace(".md", "").replace("Do-", "");
> >            fileLinks.push(`[[${filePath}|${displayName}]] (${fileData.count} 条)`);
> >        }
> >        fileLinks.sort();
> >        dv.paragraph(`以下 ${untaggedFiles.size} 个文件包含未标记条目：`);
> >        dv.paragraph(fileLinks);
> >    } else if (primaryTagItems.length > 0) {
> >        
> >        // 输出主要标签的条目原始信息
> >        dv.el("p",`${primaryTag.replace("#","")}成果：已完成 ${primaryTagItems.length} 个；`);
> >        dv.paragraph(primaryTagItems);
> >       
> >    } else {
> >        if (totalEntries > 0) {
> >            dv.paragraph(`本周无**${primaryTag.replace("#","")}型**成果输出`);
> >        } else {
> >            dv.paragraph("本周没有找到任何条目");
> >        }
> >    }
> >    // 未完成任务统计
> >	const planPath = `2-项目/${projectName}/1-每周规划`;
> >	const planFile = dv.page(`${planPath}/${planFileName}`);
> >	const taskSection = planFile.file.tasks
> >	?.filter(t => t.header?.subpath?.toLowerCase().includes("任务"))
> >	.filter(t =>t.text?.includes(`⟦${primaryTag.replace("#","")}⟧`));
> >	const targetTask = taskSection.filter(t => !t.checked);
> >	// 输出任务
> >	dv.el("p",`${primaryTag.replace("#","")}任务：本周新增任务 ${taskSection.length} 条，` 
> >	+ `其中 ${targetTask.length} 条未完成；`);
> >	dv.taskList(targetTask,false);
> >}
> > ```
> 
> > [!warning] 技术债
> > ```dataviewjs
> >// 配置：设置主要关注的标签类型
> >const primaryTag = "#技术债"; // 当日程条目都已标记时，筛选此标签
> >// 获取当前笔记文件名并解析周数信息
> >const fileName = dv.current().file.name;
> >const weekMatch = fileName.match(/Replay-(\d{4})-WK(\d+)/);
> >const planFileName = `${fileName.replace("Replay","Plan")}.md`;
> >if (!weekMatch) {
> >    dv.paragraph("**错误**：文件名格式不符合要求：Replay-YYYY-WKWW");
> >} else {
> >    const year = parseInt(weekMatch[1]);
> >    const currentWeek = parseInt(weekMatch[2]);
> >    const projectName = dv.current().file.path.split("/")[1];
> >    const path = `2-项目/${projectName}/2-每日执行`;
> >    // 计算ISO周数的函数
> >    function getISOWeek(date) {
> >        const d = new Date(date);
> >        d.setHours(0,0,0,0);
> >        d.setDate(d.getDate() + 4 - (d.getDay() || 7));
> >        const yearStart = new Date(d.getFullYear(),0,1);
> >        const weekNo = Math.ceil(((d - yearStart) / 86400000 + 1)/7);
> >        return weekNo;
> >    }
> >    // 获取该路径下的所有文件
> >    const allFiles = dv.pages(`"${path}"`).file;
> >    // 存储结果
> >    const untaggedFiles = new Map(); // 未打标签的文件
> >    const primaryTagItems = [];      // 主要标签的条目原始内容
> >    let hasUntaggedEntries = false;  // 是否有未标记条目
> >    let totalEntries = 0;            // 总条目数
> >    // 筛选属于指定年份和周数的文件
> >    for (let file of allFiles) {
> >        const dateMatch = file.name.match(/Do-(\d{4})-(\d{2})-(\d{2})/);
> >        if (!dateMatch) continue;
> >        try {
> >            const dateStr = `${dateMatch[1]}-${dateMatch[2]}-${dateMatch[3]}`;
> >            const fileDate = new Date(dateStr);
> >            if (isNaN(fileDate.getTime())) continue;
> >            // 获取文件的年份和周数
> >            const fileYear = fileDate.getFullYear();
> >            const fileWeek = getISOWeek(fileDate);
> >            // 检查是否匹配当前年份和周数
> >            if (fileYear === year && fileWeek === currentWeek) {
> >                let untaggedCount = 0;
> >                // 检查文件中的列表项
> >                if (file.lists) {
> >                    for (let list of file.lists) {
> >                        if (list.header?.subpath?.includes("输出") && 
> >                            list.task === false &&
> >                            list.text) {
> >                            totalEntries++;
> >                            // 检查条目标签状态
> >                            const hasTag = list.text.includes("#阻碍") || list.text.includes("#技术债") || 
> >                                         list.text.includes("#目标");
> >                            if (!hasTag) {
> >                                untaggedCount++;
> >                                hasUntaggedEntries = true;
> >                            } else if (list.text.includes(primaryTag)) {
> >                                const cleanText = list.text.replace(primaryTag, "").trim();
> >                                primaryTagItems.push(cleanText);
> >                            }
> >                        }
> >                    }
> >                }
> >                // 记录未打标签的文件
> >                if (untaggedCount > 0) {
> >                    untaggedFiles.set(file.path, {
> >                        fileName: file.name,
> >                        count: untaggedCount
> >                    });
> >                }
> >            }
> >        } catch (e) {
> >            console.error("Error processing file:", file.name, e);
> >        }
> >    }
> >    // 智能输出结果
> >    if (untaggedFiles.size > 0) {
> >        // 输出未打标签的文件信息
> >        const fileLinks = [];
> >        for (let [filePath, fileData] of untaggedFiles.entries()) {
> >            const displayName = fileData.fileName.replace(".md", "").replace("Do-", "");
> >            fileLinks.push(`[[${filePath}|${displayName}]] (${fileData.count} 条)`);
> >        }
> >        fileLinks.sort();
> >        dv.paragraph(`以下 ${untaggedFiles.size} 个文件包含未标记条目：`);
> >        dv.paragraph(fileLinks);
> >    } else if (primaryTagItems.length > 0) {
> >        
> >        // 输出主要标签的条目原始信息
> >        dv.el("p",`${primaryTag.replace("#","")}成果：已完成 ${primaryTagItems.length} 个；`);
> >        dv.paragraph(primaryTagItems);
> >       
> >    } else {
> >        if (totalEntries > 0) {
> >            dv.paragraph(`本周无**${primaryTag.replace("#","")}型**成果输出`);
> >        } else {
> >            dv.paragraph("本周没有找到任何条目");
> >        }
> >    }
> >    // 未完成任务统计
> >	const planPath = `2-项目/${projectName}/1-每周规划`;
> >	const planFile = dv.page(`${planPath}/${planFileName}`);
> >	const taskSection = planFile.file.tasks
> >	?.filter(t => t.header?.subpath?.toLowerCase().includes("任务"))
> >	.filter(t =>t.text?.includes(`⟦${primaryTag.replace("#","")}⟧`));
> >	const targetTask = taskSection.filter(t => !t.checked);
> >	// 输出任务
> >	dv.el("p",`${primaryTag.replace("#","")}任务：本周新增任务 ${taskSection.length} 条，` 
> >	+ `其中 ${targetTask.length} 条未完成；`);
> >	dv.taskList(targetTask,false);
> >}
> > ```
# 2. [[字段提示信息表#KR进度描述 b44aa3|KR进度]] 

| 序号  | 成果（✅）                                                         | 类型  | [[PKMS-首页#1. OKR设定\|关联KR]] | [[字段提示信息表#KR进度描述 b44aa3\|价值描述]]                                                                                                                                                              | 风险预警                                                   | 下一行动（方向）                                      |
| --- | :------------------------------------------------------------ | --- | -------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------ | --------------------------------------------- |
| 1   | 1、「改进待办事项」<br>2、优化后的「每周计划」<br>3、优化后的「每日执行」<br>4、「洞见/任务自动创建脚本」 | 目标  | KR1：探索PKMS系统的基本内容结构（组件）    | 1、将想法独立管理，脱离组件束缚，解放了想法产生来源的限制，提高了管理效率<br>2、明确了由记录到改进的核心元素及其工作流，并形成闭环<br>3、提升「每日执行」信息查询效率，避免信息“堆积”在一起<br>4、提升了独立管理想法时，记录环节的效率<br>5、进一步明确的“KR进度”、“交付异常”的核心构成字段<br>6、提升了「每周评审」与「每周计划」之间相互流转的顺畅度 | 1、”洞见/发现“与”可执行任务“会引发管理混乱<br>2、执行脚本的快捷键数量增加，提高了使用时的认知负担 | 1、探索"可执行任务”对想法记录的负面影响并消除<br>2、探索降低脚本使用认知负担的方案 |
| 2   | 「看板工具」                                                        | 目标  | O：搭建个人知识管理系统               | 1、提升了系统的整体性能                                                                                                                                                                                 |                                                        |                                               |
# 3. 交付异常

| 序号  | 成果/任务（🌗❌） | 类型  | 交付状态 | 关联障碍 | 根因分析 | 下一步行动 |
| --- | ---------- | --- | ---- | ---- | ---- | ----- |
|     |            |     |      |      |      |       |
