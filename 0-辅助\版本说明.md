# 📋 1. 系统概述

本系统是一个基于 Obsidian 的个人任务管理和知识管理系统，采用模块化设计，支持项目管理、知识积累、过程资产管理等功能。

# 📈 2. 版本历史

v1.0 (2025-07-24) - 系统整合与优化版本
- ✅ 项目管理脚本系统完善
- ✅ 脚本换行问题修复
- ✅ 代码逻辑优化和简化
- ✅ 错误处理机制完善
- ✅ 用户体验优化

v0.9 (2025-07-18~21) - 脚本系统成熟期
- ✅ 障碍日志创建脚本完善
- ✅ 快速记录突发灵感脚本优化
- ✅ 脚本使用说明文档完善

v0.8 (2025-07-03) - 技术债管理系统建立
- ✅ 技术债创建脚本开发
- ✅ 技术债任务规划工作流建立
- ✅ 技术债管理文档体系

v0.7 (2025-06-14) - 思维导图工具集成
- ✅ Mindmap format 工具开发
- ✅ 可视化思维整理功能

v0.6 (2025-WK29) - 高级分析功能期（基于每周回顾 WK29 的功能特性）
- ✅ 阻碍优先级算法实现
- ✅ 技术债TOP排序功能
- ✅ 元数据完整性检查
- ✅ Dashboard 集成分析

v0.5 (2025-WK26~28) - 组件优化期（基于每周评审 WK26-28 的改进）
- ✅ 每周复盘组件简化
- ✅ 每周回顾组件效率提升
- ✅ 字段提示信息表建立
- ✅ 看板工具集成
- ✅ 组件关键字段信息提醒优化

v0.4 (2025-WK24~25) - 数据统计增强期
- ✅ 目标异动跟踪功能
- ✅ ISO周数计算标准化
- ✅ 数据汇总算法优化

v0.3 (2025-WK22~23) - 基础工作流建立期（基于最早的每周评审记录）
- ✅ PKMS系统基础架构搭建
- ✅ KR进度跟踪机制
- ✅ 成果验收标准建立
- ✅ 每周评审模板确立
- ✅ 项目组件化设计

v0.2 (2025-05~06) - 系统设计期
- ✅ 目录结构设计
- ✅ 项目管理框架建立
- ✅ 知识管理分类体系
- ✅ GTD工作流集成

v0.1 (2025-04~05) - 系统初创期
- ✅ 基础文件夹结构
- ✅ 核心概念确立
- ✅ 初始模板设计

# 🏗️ 3. 系统架构

### 核心目录结构

```
任务管理/
├── 0-辅助/                    # 系统辅助工具
├── 1-Inbox/                   # 信息收集入口
├── 2-项目/                    # 项目管理
├── 3-过程资产/                # 过程资产管理
├── 4-知识管理/                # 知识库
└── 5-资料/                    # 参考资料
```

（1）辅助工具模块 (0-辅助)

📜 Templater 脚本系统
- **projectManager.js** - 项目管理脚本（v1.0 简化版）
- **addInsight.js** - 快速记录突发灵感脚本
- **createTechDebt.js** - 技术债创建脚本
- **createdBlocker.js** - 障碍日志创建脚本
- **globalTechDebtWorkflow.js** - 技术债任务规划工作流

（2）信息收集模块 (1-Inbox)

📜GTD 工作流
- 1-收集箱.md - 信息收集入口
- 2-项目清单.md - 项目管理清单
- 3-Todo清单.md - 任务管理清单
- 4-"将来也许"清单.md - 未来规划清单

（3）项目管理模块 (2-项目)

📜项目结构（每个项目包含以下标准结构）：
- 项目首页 - 项目概览和进度跟踪
- 1-每周计划/ - 周计划管理
- 2-每日执行/ - 日常任务执行
- 3-每周评审/ - 周评审记录
- 4-每周回顾/ - 周回顾总结
- 改进待办事项.md - 项目改进管理

（4）知识管理模块 (4-知识管理)

📜知识分类
- 1-问题库/ - 问题收集和解决方案
- 2-方法库/ - 方法论和最佳实践
- 3-案例库/ - 实际案例和经验
- 4-SOP库/ - 标准操作程序

（5）资料管理模块 (5-资料)

- 外部参考资料存储区域

# ⚡ 4. 核心特性

（1）🚀 自动化工作流

1. **项目管理自动化**
   - 一键创建新项目
   - 自动生成项目文件结构
   - 基于日期的文件命名

2. **模板化管理**
   - 标准化的项目模板
   - 知识管理模板
   - 过程资产模板

3. **脚本化操作**
   - Templater 脚本集成
   - 快速记录功能
   - 自动化文件创建

（2）📊 数据管理

1. **结构化存储**
   - 分层目录结构
   - 标准化命名规范
   - 模块化设计

2. **关联性管理**
   - 项目与过程资产关联
   - 知识与实践关联
   - 问题与解决方案关联

（3）🔄 工作流集成

1. **GTD 方法论**
   - 收集-处理-组织-回顾循环
   - 项目与任务分离
   - 上下文管理

2. **敏捷实践**
   - 周计划-日执行-周评审循环
   - 持续改进机制
   - 阻碍识别和解决

# 🎯 5. 关键里程碑

```mermaid
timeline
    title 任务管理系统发展历程

    2025-04 : 系统初创
            : 基础架构

    2025-05 : 框架建立
            : GTD集成

    2025-WK22 : 工作流确立
             : PKMS基础

    2025-WK26 : 组件优化
             : 效率提升

    2025-07-03 : 技术债系统
              : 自动化脚本

    2025-07-24 : 系统成熟
              : 完整生态
```
# 🎯 6. 使用指南

（1）快速开始
1. 使用 `projectManager.js` 创建新项目
2. 通过 GTD 收集箱收集信息
3. 使用项目模板进行周计划
4. 记录每日执行情况
5. 进行周评审和回顾

（2）最佳实践
1. 保持目录结构的一致性
2. 使用标准化的命名规范
3. 定期进行系统维护
4. 持续优化工作流程