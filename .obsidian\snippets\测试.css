/* 修复DataviewJS表格内容截断问题并实现列宽自适应 */
.dataview table {
    table-layout: auto !important; /* 关键修改：改为自动布局 */
    width: 100% !important;
    border-collapse: collapse;
}

.dataview td, .dataview th {
    white-space: normal !important;
    word-wrap: break-word !important;
    overflow-wrap: anywhere !important;
    min-width: 80px !important; /* 适当增加最小宽度 */
    padding: 12px 15px !important; /* 增加内边距提高可读性 */
}

/* 表头样式增强 */
.dataview th {
    background-color: #f5f7fa !important;
    font-weight: 600 !important;
    position: sticky;
    top: 0;
}

/* 鼠标悬停效果 */
.dataview tr:hover td {
    background-color: #edf2f7 !important;
}

/* 添加滚动容器防止宽表格溢出 */
.table-container {
    overflow-x: auto;
    max-width: 100%;
}