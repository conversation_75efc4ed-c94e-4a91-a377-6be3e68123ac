---
homePageLink: "[[PKMS-首页]]"
---
# 1. [[字段提示信息表#周目标描述： f37322|周目标]] 

- 为减少「每周计划」环节的阻塞，于本周五18:00前完成以下交付物（1、2、3）
- 为提升「每周评审」环节工作效率，于本周五18:00前完成以下交付物（4）
- 为提升「每周回顾」环节工作效率，于本周五18:00前完成以下交付物（5、6）
- 为提升项目文档创建效率，于本周五18:00前完成以下交付物（7）

# 2. [[字段提示信息表#验收标准描述 19887e|验收标准]] 
| 序号  | 交付物          | 标准/通过条件                                                                                                                                                           | 备注  |
| --- | ------------ | ----------------------------------------------------------------------------------------------------------------------------------------------------------------- | --- |
| 1   | 「改善工作流」V1.1  | 优化后的工作流包含新增以下节点极功能<br>（1）「每周计划」环节针对改善的具体执行动作<br>（2）逻辑顺畅，无额外组件新增                                                                                                   |     |
| 2   | 「每周计划」模板V2.2 | 1、优化后的模板包含「改善工作流」V1.1新增的节点的功能<br>2、“改进事项”查询代码排序优化：未标记优先级不显示                                                                                                       |     |
| 3   | 「技术债任务创建脚本」  | 调整“技术债”任务脚本创建逻辑：不再在任务前添加`〖技术债〗`标记                                                                                                                                 |     |
| 4   | 「每周评审」模板V2.2 | 重构看板数据统逻辑：<br>（1）成果列表按照名称排序<br>（2）成果统计简化：不再依赖标签筛选、不再分类显示<br>（3）任务汇总条件简化：不再依赖特定的标记、不再分类显示<br>（4）成果列表与任务统计需分离显示                                                     |     |
| 5   | 「每周回顾」模板V1.2 | 修复、优化看板数据统计异常问题：<br>（1）调整优先级排序规则：由按照`类型`排序变更为 $损耗值^2*频率/解决成本$<br>（2）显示结果限制TOP3<br>优化"改善项梳理"统计功能<br>（1）查询结果显示所有“行动项”和优先级信息<br>（2）可直接编辑行动项优先级信息<br>修复“改善回顾”统计内容丢失问题 |     |
| 6   | 「灵感记录脚本」V2.1 | 新增「改进待办事项」打开功能<br>（1）在选择弹窗增加”打开文档“选项<br>（2）选择后直接在新标签页打开目标文件                                                                                                       |     |
| 7   | 「核心组件文件创建脚本」 | 脚本核心功能：<br>（1）使用快捷键`ail+2`执行脚本<br>（2）提供用户输入弹窗创建项目名称<br>（3）提供已创建项目的选择弹窗<br>（4）提供项目核心组件的选择弹窗<br>（5）根据模板创建目标文件<br>（6）检查目标文件路径，补全异常目录结构                               |     |
# 3. 改进事项
```tasks
not done
limit 5
heading includes 行动
description regex matches /\S/
hide backlink
is not blocked
priority is not none
sort by priority
filter by function \
const projectName = query.file.path.split("/")[1]; \
const targetPath = `2-项目/${projectName}/改进待办事项`; \
const pathMatches = task.file.path.includes(targetPath);\
return pathMatches;
```

# 4. 技术债
```dataviewjs
	// 统一配置：类型和优先级设置
	const TECH_DEBT_CONFIG = {
	    types: {
	        presets: ["阻塞型", "成本型", "战略型", "无害型"],
	        get set() { return new Set(this.presets); }
	    },
	    priorities: {
	        presets: ["立即", "高", "中", "低"],
	        get set() { return new Set(this.presets); },
	        get order() { 
	            const order = {};
	            this.presets.forEach((p, i) => order[p] = i + 1);
	            return order;
	        }
	    }
	};
	
	// 获取技术债文件
	const projectName = dv.current().file.path.split("/")[1];
	const folderPath = `3-过程资产/${projectName}/技术债`;
	const allTechDebtFiles = dv.pages(`"${folderPath}"`)
	    .filter(p => 
	        p.file.name.match(/^td-\d{8}-\d{2}$/) && 
	        p.status !== "已解决" &&
	        p.file.name !== "dashboard"
	    );
	
	// 检测非预设值文件
	const hasInvalidValue = (file) => {
	    const type = file.type?.toString();
	    const priority = file.priority?.toString();
	    
	    return !TECH_DEBT_CONFIG.types.set.has(type) || 
	           !TECH_DEBT_CONFIG.priorities.set.has(priority);
	};
	
	const invalidFiles = allTechDebtFiles.filter(hasInvalidValue);
	
	// 辅助函数：获取类型显示值（仅显示预设值或提示信息）
	const getTypeDisplay = (file) => {
	    const type = file.type?.toString();
	    if (!type) return "(未设置类型)";
	    return TECH_DEBT_CONFIG.types.set.has(type) ? type : "(无效类型)";
	};
	
	// 辅助函数：获取优先级显示值（仅显示预设值或提示信息）
	const getPriorityDisplay = (file) => {
	    const priority = file.priority?.toString();
	    if (!priority) return "(未设置优先级)";
	    return TECH_DEBT_CONFIG.priorities.set.has(priority) ? priority : "(无效优先级)";
	};
	
	// 显示结果
	if (invalidFiles.length > 0) {
	    // 输出非预设值文件  
	    const listItems = invalidFiles.map(file => {
	        const displayName = file.aliases || file.file.name;
	        const typeDisplay = getTypeDisplay(file);
	        const priorityDisplay = getPriorityDisplay(file);
	        return `[[${file.file.name}|${displayName}]] - ${typeDisplay} - ${priorityDisplay}`;
	    });
	    
	    // 使用单个Markdown列表输出
	    dv.paragraph(listItems);
	} else {
	    // 如果没有非预设值文件，则按原逻辑处理预设值文件
	    const validFiles = allTechDebtFiles;
	    // 确定目标类型
	    let targetType = null;
	    for (const type of TECH_DEBT_CONFIG.types.presets) {
	        if (validFiles.some(p => p.type === type)) {
	            targetType = type;
	            break;
	        }
	    }
	    if (!targetType) {
	        dv.paragraph("⚠️ 没有待处理的技术债");
	    } else {
	        // 过滤并排序文件
	        const filteredFiles = validFiles
	            .filter(p => p.type === targetType)
	            .sort(p => TECH_DEBT_CONFIG.priorities.order[p.Priority] || 999);
	        
	        if (filteredFiles.length === 0) {
	            dv.paragraph(`⚠️ 没有${targetType}类型的技术债`);
	        } else {          
	            // 创建优先级分组
	            const groupedByPriority = {};
	            filteredFiles.forEach(file => {
	                const priority = file.priority || "未设置";
	                if (!groupedByPriority[priority]) {
	                    groupedByPriority[priority] = [];
	                }
	                groupedByPriority[priority].push(file);
	            });
	            
	            // 收集所有输出行
	            const outputLines = [];
	            TECH_DEBT_CONFIG.priorities.presets.forEach(priority => {
	                if (groupedByPriority[priority]) {
	                    groupedByPriority[priority].forEach(file => {
	                        const displayName = file.aliases || file.file.name;
	                        outputLines.push(
	                            `[[${file.file.name}|${displayName}]] - ${file.type} - ${priority}`
	                        );
	                    });
	                }
	            });
	            // 使用单个Markdown列表输出所有内容
	            dv.paragraph(outputLines);
	        }
	    }
	}
```

# 5. 任务拆解

- [x] 优化“改进事项”代码（隐藏未标记优先级的任务） ⏳ 2025-07-21 ✅ 2025-07-21
- [x] 探索并优化「改善工作流」有关「每周计划」“改善”环节的节点 ⏳ 2025-07-21 ✅ 2025-07-21
- [x] 优化「每周计划」模板有关“改善”的内容 ⏳ 2025-07-21 ✅ 2025-07-21
- [x] 调整“技术债”任务脚本创建逻辑 ⏳ 2025-07-21 ✅ 2025-07-21
- [x] 优化「每周回顾」"改善项梳理"统计功能 ⏳ 2025-07-21 ✅ 2025-07-21
- [x] 优化「灵感记录脚本」 ⏳ 2025-07-21 ✅ 2025-07-21
- [x] 按照最新的「障碍日志模板」重新梳理记录的阻碍内容 ⏳ 2025-07-21 ✅ 2025-07-22
- [x] 重新梳理所有阻碍别名 ⏳ 2025-07-21 ✅ 2025-07-22
- [x] 重构「每周评审」看板数据统逻辑 ⏳ 2025-07-22 ✅ 2025-07-22
- [x] 更新「每周评审」模板 ⏳ 2025-07-22 ✅ 2025-07-22
- [x] 修复、优化「每周回顾」看板数据统计异常问题 ⏳ 2025-07-22 ✅ 2025-07-23
- [x] 修复「每周回顾」“改善回顾”统计内容丢失问题 ⏳ 2025-07-22 ✅ 2025-07-23
- [x] 更新「每周回顾」模板 ⏳ 2025-07-22 ✅ 2025-07-23
- [x] 统一项目文件夹下子文件夹的名称，并同步调整组件的查询代码 ⏳ 2025-07-24 ✅ 2025-07-24
- [ ] 编写「核心组件文件创建脚本」 ⏳ 2025-07-24