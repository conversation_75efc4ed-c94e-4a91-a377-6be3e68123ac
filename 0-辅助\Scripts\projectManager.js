/**
 * 项目管理脚本 - 简化版
 */

module.exports = async (tp) => {
  // 配置映射
  const configs = {
    每周计划: {
      template: "TP-Project-Plan",
      folder: "1-每周计划",
      prefix: "Plan",
    },
    每日执行: { template: "TP-Project-Do", folder: "2-每日执行", prefix: "Do" },
    每周评审: {
      template: "TP-Project-Replay",
      folder: "3-每周评审",
      prefix: "Replay",
    },
    每周回顾: {
      template: "TP-Project-Review",
      folder: "4-每周回顾",
      prefix: "Review",
    },
  };

  try {
    // 选择操作类型
    const action = await tp.system.suggester(
      [
        "➕创建新项目",
        "【1】每周计划",
        "【2】每日执行",
        "【3】每周评审",
        "【4】每周回顾",
      ],
      ["创建新项目", "每周计划", "每日执行", "每周评审", "每周回顾"]
    );

    if (!action) return;

    if (action === "创建新项目") {
      await createNewProject();
    } else {
      await createProjectFile(action);
    }
  } catch (error) {
    new Notice(`操作失败: ${error.message}`);
  }

  // 创建新项目
  async function createNewProject() {
    const name = await tp.system.prompt("请输入项目名称:");
    if (!name?.trim()) return new Notice("项目名称不能为空");

    const projectPath = `2-项目/${name.trim()}`;
    if (app.vault.getAbstractFileByPath(projectPath)) {
      return new Notice(`项目"${name.trim()}"已存在`);
    }

    await app.vault.createFolder(projectPath);
    const file = await createFile(
      `${projectPath}/${name.trim()}-首页.md`,
      "TP-Project-首页"
    );
    if (file) {
      await app.workspace.getLeaf("tab").openFile(file);
      new Notice(`项目"${name.trim()}"创建成功`);
    }
  }

  // 创建项目文件
  async function createProjectFile(fileType) {
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) return new Notice("请先打开一个项目文件");

    const pathParts = activeFile.path.split("/");
    if (pathParts[0] !== "2-项目") {
      return new Notice("请在项目文件夹中执行此操作");
    }

    const projectName = pathParts[1];
    const config = configs[fileType];

    // 生成文件名
    const now = new Date();
    const fileName =
      fileType === "每日执行"
        ? `${config.prefix}-${now.toISOString().split("T")[0]}.md`
        : `${config.prefix}-${now.getFullYear()}-WK${getWeekNumber(now)
            .toString()
            .padStart(2, "0")}.md`;

    const folderPath = `2-项目/${projectName}/${config.folder}`;
    const filePath = `${folderPath}/${fileName}`;

    // 确保文件夹存在
    if (!app.vault.getAbstractFileByPath(folderPath)) {
      await app.vault.createFolder(folderPath);
    }

    // 检查文件是否已存在
    const existingFile = app.vault.getAbstractFileByPath(filePath);
    if (existingFile) {
      await app.workspace.getLeaf("tab").openFile(existingFile);
      return new Notice(`已打开现有的${fileType}文件`);
    }

    // 创建新文件
    const file = await createFile(filePath, config.template);
    if (file) {
      await app.workspace.getLeaf("tab").openFile(file);
      new Notice(`${fileType}文件创建成功`);
    }
  }

  // 根据模板创建文件
  async function createFile(filePath, templateName) {
    try {
      const templatePath = `0-辅助/Templater/Notes/${templateName}.md`;
      const templateFile = app.vault.getAbstractFileByPath(templatePath);

      if (!templateFile) {
        new Notice(`模板文件不存在: ${templatePath}`);
        return null;
      }

      const content = await app.vault.read(templateFile);
      return await app.vault.create(filePath, content);
    } catch (error) {
      new Notice(`创建文件失败: ${error.message}`);
      return null;
    }
  }

  // 获取ISO周数
  function getWeekNumber(date) {
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    d.setDate(d.getDate() + 3 - ((d.getDay() + 6) % 7));
    const week1 = new Date(d.getFullYear(), 0, 4);
    return (
      1 +
      Math.round(((d - week1) / 86400000 - 3 + ((week1.getDay() + 6) % 7)) / 7)
    );
  }
};
