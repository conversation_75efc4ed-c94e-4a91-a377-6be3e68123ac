/**
 * 项目管理脚本
 * 功能：
 * 1. 弹出选择弹窗让用户选择"➕创建新项目"、"【1】每周计划"、"【2】每日执行"、"【3】每周评审"、"【4】每周回顾"
 * 2. 根据选择创建相应的文件并在新标签页打开
 */

module.exports = async (tp) => {
  try {
    // 步骤1：弹出选择弹窗，让用户选择操作类型
    const actionType = await tp.system.suggester(
      [
        "➕创建新项目",
        "【1】每周计划", 
        "【2】每日执行",
        "【3】每周评审", 
        "【4】每周回顾"
      ],
      [
        "创建新项目",
        "每周计划",
        "每日执行", 
        "每周评审",
        "每周回顾"
      ]
    );

    // 如果用户取消了选择，则退出脚本
    if (!actionType) {
      new Notice("已取消选择");
      return null;
    }

    // 步骤2：根据选择执行相应操作
    if (actionType === "创建新项目") {
      return await createNewProject(tp);
    } else {
      return await createProjectFile(tp, actionType);
    }

  } catch (error) {
    console.error("项目管理脚本执行出错:", error);
    new Notice(`操作失败: ${error.message}`);
    return null;
  }

  // 创建新项目的函数
  async function createNewProject(tp) {
    try {
      // 弹出输入弹窗让用户输入项目名称
      const projectName = await tp.system.prompt("请输入项目名称:");

      // 如果用户取消了输入或输入为空，则退出
      if (!projectName || projectName.trim() === "") {
        new Notice("项目名称不能为空，已取消创建");
        return null;
      }

      const trimmedProjectName = projectName.trim();
      const projectPath = `2-项目/${trimmedProjectName}`;

      // 检查项目文件夹是否已存在
      const existingFolder = app.vault.getAbstractFileByPath(projectPath);
      if (existingFolder) {
        new Notice(`项目"${trimmedProjectName}"已存在`);
        return null;
      }

      // 创建项目文件夹
      await app.vault.createFolder(projectPath);

      // 根据模板"TP-Project-首页"创建首页文件
      const homepageFileName = `${trimmedProjectName}-首页.md`;
      const homepageFilePath = `${projectPath}/${homepageFileName}`;
      
      const targetFile = await createFileFromTemplate(
        homepageFilePath, 
        "TP-Project-首页"
      );

      if (!targetFile) {
        new Notice("创建项目首页失败");
        return null;
      }

      // 在新标签页打开文件
      const leaf = app.workspace.getLeaf("tab");
      await leaf.openFile(targetFile);

      new Notice(`项目"${trimmedProjectName}"创建成功`);
      return true;

    } catch (error) {
      console.error("创建新项目时出错:", error);
      new Notice(`创建项目失败: ${error.message}`);
      return null;
    }
  }

  // 创建项目文件的函数
  async function createProjectFile(tp, fileType) {
    try {
      // 获取当前活动文件
      const activeFile = app.workspace.getActiveFile();
      if (!activeFile) {
        new Notice("请先打开一个项目文件");
        return null;
      }

      // 从路径中提取项目名称
      const pathParts = activeFile.path.split("/");
      if (pathParts.length < 2 || pathParts[0] !== "2-项目") {
        new Notice("请在项目文件夹中的文件内执行此操作");
        return null;
      }
      
      const projectName = pathParts[1];

      // 文件配置映射
      const fileConfigs = {
        "每周计划": {
          template: "TP-Project-Plan",
          subFolder: "1-每周计划",
          fileNamePrefix: "Plan"
        },
        "每日执行": {
          template: "TP-Project-Do", 
          subFolder: "2-每日执行",
          fileNamePrefix: "Do"
        },
        "每周评审": {
          template: "TP-Project-Replay",
          subFolder: "3-每周评审", 
          fileNamePrefix: "Replay"
        },
        "每周回顾": {
          template: "TP-Project-Review",
          subFolder: "4-每周回顾",
          fileNamePrefix: "Review"
        }
      };

      const config = fileConfigs[fileType];
      if (!config) {
        new Notice(`不支持的文件类型: ${fileType}`);
        return false;
      }

      // 生成文件名（基于当前日期）
      const now = new Date();
      let fileName;
      
      if (fileType === "每日执行") {
        // 每日执行使用日期格式：Do-YYYY-MM-DD.md
        const dateStr = now.toISOString().split('T')[0];
        fileName = `${config.fileNamePrefix}-${dateStr}.md`;
      } else {
        // 其他文件使用周格式：Prefix-YYYY-WKWW.md
        const year = now.getFullYear();
        const weekNumber = getWeekNumber(now);
        const weekStr = weekNumber.toString().padStart(2, '0');
        fileName = `${config.fileNamePrefix}-${year}-WK${weekStr}.md`;
      }

      // 构建完整路径
      const subFolderPath = `2-项目/${projectName}/${config.subFolder}`;
      const filePath = `${subFolderPath}/${fileName}`;

      // 确保子文件夹存在
      const subFolder = app.vault.getAbstractFileByPath(subFolderPath);
      if (!subFolder) {
        await app.vault.createFolder(subFolderPath);
      }

      // 检查文件是否已存在
      const existingFile = app.vault.getAbstractFileByPath(filePath);
      if (existingFile) {
        // 如果文件已存在，直接打开
        const leaf = app.workspace.getLeaf("tab");
        await leaf.openFile(existingFile);
        new Notice(`已打开现有的${fileType}文件`);
        return true;
      }

      // 根据模板创建文件
      const targetFile = await createFileFromTemplate(filePath, config.template);
      if (!targetFile) {
        new Notice(`创建${fileType}文件失败`);
        return false;
      }

      // 在新标签页打开文件
      const leaf = app.workspace.getLeaf("tab");
      await leaf.openFile(targetFile);

      new Notice(`${fileType}文件创建成功`);
      return true;

    } catch (error) {
      console.error(`创建${fileType}文件时出错:`, error);
      new Notice(`创建${fileType}文件失败: ${error.message}`);
      return false;
    }
  }

  // 根据模板创建文件的辅助函数
  async function createFileFromTemplate(targetPath, templateName) {
    try {
      const templatePath = `0-辅助/Templater/Notes/${templateName}.md`;
      const templateFile = app.vault.getAbstractFileByPath(templatePath);

      if (!templateFile) {
        new Notice(`模板文件不存在: ${templatePath}`);
        return null;
      }

      // 读取模板内容
      const templateContent = await app.vault.read(templateFile);

      // 创建目标文件
      const createdFile = await app.vault.create(targetPath, templateContent);

      return createdFile;
    } catch (error) {
      console.error("根据模板创建文件时出错:", error);
      new Notice(`创建文件失败: ${error.message}`);
      return null;
    }
  }

  // 获取ISO周数的辅助函数
  function getWeekNumber(date) {
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7);
    const week1 = new Date(d.getFullYear(), 0, 4);
    return 1 + Math.round(((d - week1) / 86400000 - 3 + (week1.getDay() + 6) % 7) / 7);
  }
};
