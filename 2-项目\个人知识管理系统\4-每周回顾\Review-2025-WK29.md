---
homePageLink: "[[PKMS-首页]]"
---
# 1. 类型分析
> [!dashboard]
> 
> > [!tip] 阻碍
> >```dataviewjs
> >// 定义目标文件夹和文件名格式
> >const projectName = dv.current().file.path.split("/")[1];
> >const folder = `3-过程资产/${projectName}/阻碍`;
> >const pattern = /^blocker-\d{8}-\d{2}$/;
> >const allFiles = dv.pages(`"${folder}"`);
> >
> >// 筛选符合文件名格式且状态非"已关闭"的文件
> >const filteredFiles = allFiles.filter(p => {
> >    const name = p.file.name.replace(/\.md$/, ""); 
> >    return pattern.test(name) && p.status !== "已关闭" && p.status !== "待验证";
> >});
> >
> >if (filteredFiles.length === 0) {
> >    dv.paragraph("没有找到符合条件的阻碍文件");
> >} else {
> >    // 标识是否存在元数据缺失的文件
> >    let hasMissingMetadata = false;
> >    
> >    // 存储元数据完整的文件及其计算的优先级
> >    const filesWithPriority = [];
> >    
> >    // 当前日期用于计算时间差
> >    const today = new Date();
> >    
> >    // 处理每个文件
> >    filteredFiles.forEach(p => {
> >        // 检查元数据是否完整
> >        const hasDrainVal = p.drain_val !== undefined && typeof p.drain_val === 'number';
> >        const hasCount = p.count !== undefined && typeof p.count === 'number';
> >        const hasCreatedDate = p.created_Date !== undefined && !isNaN(new Date(p.created_Date));
> >        const hasTimeCost = p.timeCost !== undefined && typeof p.timeCost === 'number';
> >        
> >        // 如果有任何元数据缺失，设置标志
> >        if (!hasDrainVal || !hasCount || !hasCreatedDate || !hasTimeCost) {
> >            hasMissingMetadata = true;
> >        }
> >        
> >        // 只有所有元数据都完整才计算优先级
> >        if (hasDrainVal && hasCount && hasCreatedDate && hasTimeCost) {
> >            // 计算时间差（以周为单位）
> >            const createdDate = new Date(p.created_Date);
> >            const timeDiffWeeks = (today - createdDate) / (1000 * 60 * 60 * 24 * 7);
> >            
> >            // 确保时间差至少为1周
> >            const effectiveWeeks = Math.max(timeDiffWeeks, 1);
> >            
> >            // 公式: drain_val² × (count/时间周数) ÷ timeCost
> >            const priority = (p.drain_val ** 2) * (p.count / effectiveWeeks) / p.timeCost;
> >            
> >            filesWithPriority.push({
> >                file: p,
> >                priority: priority
> >            });
> >        }
> >    });
> >    
> >    // 如果有元数据缺失，显示提示信息
> >    if (hasMissingMetadata) {
> >        dv.paragraph("部分阻碍元数据缺失，请优先补全  🔗[[dashboard]]");
> >    } 
> >    // 如果没有元数据缺失且存在优先级计算结果
> >    else if (filesWithPriority.length > 0) {
> >        // 按优先级降序排序
> >        filesWithPriority.sort((a, b) => b.priority - a.priority);
> >        
> >        // 选取TOP3
> >        const top3 = filesWithPriority.slice(0, 3);
> >        
> >        // 构建单个Markdown字符串
> >        let markdown = "TOP3优先级阻碍：\n";
> >        
> >        top3.forEach(item => {
> >            markdown += `- [[${item.file.file.path}|${item.file.aliases}]]\n`;
> >        });
> >        
> >        // 使用单个paragraph输出
> >        dv.paragraph(markdown);
> >    } 
> >    // 如果没有元数据缺失但没有计算优先级文件
> >    else {
> >        dv.paragraph("没有找到元数据完整的阻碍文件");
> >    }
> >}
> >```
> 
> > [!warning] 技术债
> > ```dataviewjs
> >// 定义目标文件夹和文件名格式
> >const projectName = dv.current().file.path.split("/")[1];
> >const folder = `3-过程资产/${projectName}/技术债`;
> >const pattern = /^td-\d{8}-\d{2}$/;
> >const allFiles = dv.pages(`"${folder}"`);
> >const combinedType = "阻塞型/成本型/战略型/无害型";
> >// 筛选符合文件名格式且状态非"已关闭"的文件
> >const filteredFiles = allFiles.filter(p => {
> >    const name = p.file.name.replace(/\.md$/, ""); 
> >    return pattern.test(name) && p.status !== "已关闭";
> >});
> >if (filteredFiles.length === 0) {
> >    dv.paragraph("没有找到符合条件的技术债文件");
> >} else {
> >    const specialFiles = filteredFiles.filter(p => !p.type || p.type === combinedType);
> >	// 输出特殊文件（无type或type为组合值）
> >    if (specialFiles.length > 0) {
> >        dv.el("p",`当前共计 **${specialFiles.length}** 个文件的类型未被定义或无效`)
> >        dv.paragraph(specialFiles.map(p => `[[${p.file.path}|${p.aliases}]]`));
> >    }else{
> >	    const groups = {};
> >	    filteredFiles.forEach(p => {
> >	        const type = p.type || "未分类";
> >	        if (!groups[type]) groups[type] = [];
> >	        groups[type].push(p);
> >	    });
> >	    // 按数量排序
> >	    const sortedGroups = Object.entries(groups)
> >	        .map(([type, files]) => ({
> >	            type,
> >	            count: files.length,
> >	            files
> >	        }))
> >	        .sort((a, b) => b.count - a.count);
> >	    // 取前三组
> >	    const topGroups = sortedGroups.slice(0, 3);
> >	    // 显示分组结果（列表形式）
> >	    topGroups.forEach(group => {
> >	        dv.el("p", `${group.type} (${group.count})`);
> >	        dv.paragraph(group.files.map(p => {return `[[${p.file.path}|${p.aliases}]]`}));
> >	    });
> >    }
> >}
> > ```

# 2. 流程改善

| 来源                      | 根因分析                                    | 改善行动                                                                                                | 验收标准                     | 改善结果 |
| ----------------------- | --------------------------------------- | --------------------------------------------------------------------------------------------------- | ------------------------ | ---- |
| [[blocker-20250707-01]] | 流程设计阶段工具配置更新时，因历史路径依赖，掉入了认知惯性陷阱（形成思维定式） | 当任何流程/工具调整涉及元素迁移（如字段、功能）时，自问<br>（1）该元素在新场景的核心目的是否变化？<br>（2）原有命名是否仍准确反映新功能？  <br>（3）是否需要剥离/重构而非直接复用？ | 在流程设计阶段工具配置更新时，明确3个问题的结果 |      |

# 3. 改善回顾

```dataviewjs
// 汇总表格数据到当前页面
const projectName = dv.current().file.path.split("/")[1].trim();
const targetFolder = `2-项目/${projectName}/4-每周回顾`;
const tableTitlePattern = "流程改善"; // 标题中包含的关键词

// 1. 从当前文件名确定周数
const currentFileName = dv.current().file.name;
const weekMatch = currentFileName.match(/^Review-(\d{4})-WK(\d{2})$/);
if (!weekMatch) {
    dv.el("p", "⚠️ 模板文件中查询不生效");
    return;
}

const targetYear = parseInt(weekMatch[1]);
const targetWeek = parseInt(weekMatch[2]);	
let allTableData = [];

// 第一步：快速筛选符合条件的文件
const candidateFiles = [];
for (let file of dv.pages(`"${targetFolder}"`).file) {
    const fileMatch = file.name.match(/^Review-(\d{4})-WK(\d{2})$/);
    if (!fileMatch) continue; // 跳过文件名不符合的文件
    
    const fileYear = parseInt(fileMatch[1]);
    const fileWeek = parseInt(fileMatch[2]);

    // 仅处理周数严格小于当前的文件
    if (fileYear < targetYear || (fileYear === targetYear && fileWeek < targetWeek)) {
        candidateFiles.push({
            file,
            year: fileYear,
            week: fileWeek,
            path: file.path
        });
    }
}

// 第二步：按时间倒序排序（从最新到最早）
candidateFiles.sort((a, b) => {
    if (a.year !== b.year) return b.year - a.year;
    return b.week - a.week;
});

// 第三步：处理符合条件的文件
for (let candidate of candidateFiles) {
    const { file, year, week, path } = candidate;
    
    // 加载文件内容
    const content = await dv.io.load(path);
    
    // 识别指定标题下的内容区域
    const headingRegex = new RegExp(
        `(?:^|\\n)#+\\s*.*${tableTitlePattern}.*[^\\n]*\\n([\\s\\S]*?)(?=\\n#|$)`, 
        "i"
    );
    
    const match = content.match(headingRegex);
    if (!match || !match[1]) continue; // 跳过没有指定标题的文件
    
    const sectionContent = match[1].trim();
    
    // 表格解析函数
    const parseMarkdownTables = (markdown) => {
        const tables = [];
        const tableRegex = /\|([^\n]+\|)\s*\n\s*\|(\s*:?[-~]+:?\s*\|)+\s*\n((?:\s*\|[^\n]+\|[^\S\r?\n]*\n?)+)/g;
        
        let tableMatch;
        while ((tableMatch = tableRegex.exec(markdown)) !== null) {
            try {
                const headerRow = tableMatch[1].split('|')
                    .map(cell => cell.trim())
                    .filter(cell => cell !== '');
                
                // 处理数据行
                const dataRows = tableMatch[3].split('\n')
                    .filter(row => row.trim() !== '' && row.includes('|') && !row.startsWith('|--'))
                    .map(row => {
                        // 处理包含换行符的单元格
                        const cells = row.split('|')
                            .slice(1, -1)
                            .map(cell => {
                                const normalized = cell.trim()
                                    .replace(/<br>/gi, '\n')
                                    .replace(/\s*\n\s*/g, '\n')
                                    .trim();
                                return normalized;
                            });
                        return cells;
                    })
                    .filter(row => row.some(cell => cell !== ''));
                
                if (dataRows.length > 0) {
                    tables.push({ header: headerRow, data: dataRows });
                }
            } catch (e) {
                console.warn("表格解析错误:", e);
            }
        }
        return tables;
    };
    
    // 解析表格
    const tables = parseMarkdownTables(sectionContent);
    
    // 处理找到的表格
    if (tables.length > 0) {
        // 创建文件链接
        const weekTag = `${year}-WK${week.toString().padStart(2, '0')}`;
        const fileName = `Review-${weekTag}.md`;
        const fileLink = dv.fileLink(`${targetFolder}/${fileName}`, false, fileName);
        
        // 处理每个表格
        for (const table of tables) {
            const { header, data } = table;
            
            // 仅在首次发现有效表格时添加表头 - 回顾链接放在最后
            if (allTableData.length === 0) {
                allTableData.push([...header, "回顾链接"]); // 修改位置
            }
            
            // 添加数据行 - 回顾链接放在最后
            data.forEach(row => {
                allTableData.push([...row, fileLink]); // 修改位置
            });
        }
    }
}	    

// 按Blocker ID排序（索引需要调整）
if (allTableData.length > 1) {
    const headers = allTableData[0];
    const dataRows = allTableData.slice(1);
    
    // 查找Blocker ID列的索引 - 注意索引变化（现在"来源"是第一列）
    const blockerIdColumnIndex = headers.indexOf("来源"); // 索引变为0
    
    // 自定义排序函数
    dataRows.sort((a, b) => {
        const blockerIdA = a[blockerIdColumnIndex] || "";
        const blockerIdB = b[blockerIdColumnIndex] || "";
        
        const dateMatchA = blockerIdA.match(/blocker-(\d{8})/);
        const dateMatchB = blockerIdB.match(/blocker-(\d{8})/);
        
        if (dateMatchA && dateMatchB) {
            return dateMatchB[1].localeCompare(dateMatchA[1]);
        } 
        else if (dateMatchA) {
            return -1;
        } 
        else if (dateMatchB) {
            return 1;
        }
        return blockerIdA.localeCompare(blockerIdB);
    });
    
    allTableData = [headers, ...dataRows];
}

// 输出结果
if (allTableData.length > 0) {
    dv.table(allTableData[0], allTableData.slice(1));
} else {
    dv.el("p", "🎉 无历史流程改善数据");
}
```

# 4. 改进项梳理 
```tasks
not done
heading includes 行动
description regex matches /\S/
hide backlink
is not blocked
priority is not none
sort by priority
filter by function \
const projectName = query.file.path.split("/")[1]; \
const targetPath = `2-项目/${projectName}/改进待办事项`; \
const pathMatches = task.file.path.includes(targetPath);\
return pathMatches;
```

# 5. 成功经验

| 超预期成果 | 根因分析 | 关键行为 | 证据链 | 经验封装 | 复用场景 |
| ----- | ---- | ---- | --- | ---- | ---- |
|       |      |      |     |      |      |